<?php
// إعدادات الموقع العامة
define('SITE_NAME', 'نقرة هوست');
define('SITE_URL', 'https://nakra.host');
define('SITE_EMAIL', '<EMAIL>');
define('SITE_PHONE', '01062751630');

// معلومات الشركة
define('COMPANY_NAME', 'نقرة للتسويق الإلكتروني');
define('COMPANY_WEBSITE', 'https://www.nakraformarketing.com');
define('HOSTMEED_WEBSITE', 'https://hostmeed.cloud');

// إعدادات الأمان
define('ENCRYPTION_KEY', 'your-secret-encryption-key-here');
define('SESSION_TIMEOUT', 3600); // ساعة واحدة

// إعدادات البريد الإلكتروني
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
define('SMTP_ENCRYPTION', 'tls');

// إعدادات الدفع
define('PAYPAL_CLIENT_ID', 'your-paypal-client-id');
define('PAYPAL_CLIENT_SECRET', 'your-paypal-client-secret');
define('PAYPAL_MODE', 'sandbox'); // sandbox أو live

// إعدادات API
define('API_KEY', 'your-api-key');
define('API_SECRET', 'your-api-secret');

// مسارات الملفات
define('UPLOAD_PATH', 'uploads/');
define('LOGS_PATH', 'logs/');
define('BACKUP_PATH', 'backups/');

// إعدادات التخزين المؤقت
define('CACHE_ENABLED', true);
define('CACHE_DURATION', 3600);

// إعدادات التطبيق
define('DEBUG_MODE', false);
define('MAINTENANCE_MODE', false);

// رسائل النظام
define('SUCCESS_MESSAGE', 'تم تنفيذ العملية بنجاح');
define('ERROR_MESSAGE', 'حدث خطأ أثناء تنفيذ العملية');
define('INVALID_LOGIN', 'بيانات تسجيل الدخول غير صحيحة');
define('ACCESS_DENIED', 'ليس لديك صلاحية للوصول لهذه الصفحة');

// أنواع الخدمات
$hosting_types = [
    'shared' => 'الاستضافة المشتركة',
    'vps' => 'السيرفرات الافتراضية',
    'dedicated' => 'السيرفرات المخصصة',
    'cloud' => 'الاستضافة السحابية'
];

// حالات الطلبات
$order_statuses = [
    'pending' => 'في الانتظار',
    'processing' => 'قيد المعالجة',
    'active' => 'نشط',
    'suspended' => 'معلق',
    'cancelled' => 'ملغي',
    'expired' => 'منتهي الصلاحية'
];

// طرق الدفع
$payment_methods = [
    'paypal' => 'PayPal',
    'bank_transfer' => 'تحويل بنكي',
    'credit_card' => 'بطاقة ائتمان',
    'vodafone_cash' => 'فودافون كاش',
    'orange_money' => 'أورانج موني'
];

// دالة للحصول على إعدادات الموقع
function getSiteSetting($key, $default = '') {
    $settings = [
        'site_name' => SITE_NAME,
        'site_url' => SITE_URL,
        'site_email' => SITE_EMAIL,
        'site_phone' => SITE_PHONE,
        'company_name' => COMPANY_NAME
    ];
    
    return isset($settings[$key]) ? $settings[$key] : $default;
}

// دالة لتشفير البيانات
function encryptData($data) {
    return openssl_encrypt($data, 'AES-256-CBC', ENCRYPTION_KEY, 0, substr(md5(ENCRYPTION_KEY), 0, 16));
}

// دالة لفك تشفير البيانات
function decryptData($data) {
    return openssl_decrypt($data, 'AES-256-CBC', ENCRYPTION_KEY, 0, substr(md5(ENCRYPTION_KEY), 0, 16));
}

// دالة لتنظيف البيانات
function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

// دالة للتحقق من صحة البريد الإلكتروني
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

// دالة لتوليد رمز عشوائي
function generateRandomToken($length = 32) {
    return bin2hex(random_bytes($length / 2));
}
?>

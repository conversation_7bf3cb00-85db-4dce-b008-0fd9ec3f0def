<?php
// معالجة تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['login'])) {
    $email = sanitizeInput($_POST['email']);
    $password = $_POST['password'];
    $remember = isset($_POST['remember']);
    
    if (empty($email) || empty($password)) {
        showMessage('يرجى إدخال البريد الإلكتروني وكلمة المرور', 'danger');
    } else {
        // البحث عن المستخدم
        $user = fetchOne("SELECT * FROM users WHERE email = ? AND status = 'active'", [$email]);
        
        if ($user && password_verify($password, $user['password'])) {
            // تسجيل الدخول بنجاح
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_name'] = $user['first_name'] . ' ' . $user['last_name'];
            $_SESSION['user_email'] = $user['email'];
            $_SESSION['user_role'] = $user['role'];
            
            // تحديث آخر تسجيل دخول
            executeQuery("UPDATE users SET last_login = NOW() WHERE id = ?", [$user['id']]);
            
            // تسجيل النشاط
            logActivity($user['id'], 'تسجيل الدخول', 'تم تسجيل الدخول بنجاح');
            
            // إعداد كوكي التذكر
            if ($remember) {
                $token = generateRandomToken();
                setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/'); // 30 يوم
                executeQuery("UPDATE users SET remember_token = ? WHERE id = ?", [$token, $user['id']]);
            }
            
            // إعادة التوجيه حسب الدور
            if ($user['role'] == 'admin') {
                redirect('admin/index.php');
            } else {
                redirect('index.php?page=dashboard');
            }
        } else {
            showMessage('بيانات تسجيل الدخول غير صحيحة', 'danger');
            logActivity(null, 'محاولة تسجيل دخول فاشلة', "البريد الإلكتروني: $email");
        }
    }
}

// التحقق من كوكي التذكر
if (!isLoggedIn() && isset($_COOKIE['remember_token'])) {
    $token = $_COOKIE['remember_token'];
    $user = fetchOne("SELECT * FROM users WHERE remember_token = ? AND status = 'active'", [$token]);
    
    if ($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_name'] = $user['first_name'] . ' ' . $user['last_name'];
        $_SESSION['user_email'] = $user['email'];
        $_SESSION['user_role'] = $user['role'];
        
        if ($user['role'] == 'admin') {
            redirect('admin/index.php');
        } else {
            redirect('index.php?page=dashboard');
        }
    }
}

// إذا كان المستخدم مسجل دخول بالفعل
if (isLoggedIn()) {
    if ($_SESSION['user_role'] == 'admin') {
        redirect('admin/index.php');
    } else {
        redirect('index.php?page=dashboard');
    }
}
?>

<!-- صفحة تسجيل الدخول -->
<div class="login-page">
    <section class="login-section py-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-5 col-md-7">
                    <div class="login-card">
                        <div class="login-header text-center mb-4">
                            <img src="assets/images/logo.png" alt="<?php echo SITE_NAME; ?>" height="60" class="mb-3">
                            <h2 class="login-title">تسجيل الدخول</h2>
                            <p class="login-subtitle text-muted">مرحباً بك في <?php echo SITE_NAME; ?></p>
                        </div>

                        <form method="POST" class="login-form needs-validation" novalidate>
                            <div class="mb-3">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope me-2"></i>
                                    البريد الإلكتروني
                                </label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       placeholder="أدخل بريدك الإلكتروني" required>
                                <div class="invalid-feedback">
                                    يرجى إدخال بريد إلكتروني صحيح
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock me-2"></i>
                                    كلمة المرور
                                </label>
                                <div class="password-input-group">
                                    <input type="password" class="form-control" id="password" name="password" 
                                           placeholder="أدخل كلمة المرور" required>
                                    <button type="button" class="password-toggle" onclick="togglePassword('password')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="invalid-feedback">
                                    يرجى إدخال كلمة المرور
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="remember" name="remember">
                                        <label class="form-check-label" for="remember">
                                            تذكرني
                                        </label>
                                    </div>
                                </div>
                                <div class="col-6 text-end">
                                    <a href="?page=forgot-password" class="forgot-password-link">
                                        نسيت كلمة المرور؟
                                    </a>
                                </div>
                            </div>

                            <button type="submit" name="login" class="btn btn-primary w-100 login-btn">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                تسجيل الدخول
                            </button>
                        </form>

                        <div class="login-divider">
                            <span>أو</span>
                        </div>

                        <div class="social-login">
                            <button class="btn btn-outline-primary w-100 mb-2 social-btn">
                                <i class="fab fa-google me-2"></i>
                                تسجيل الدخول بـ Google
                            </button>
                            <button class="btn btn-outline-info w-100 social-btn">
                                <i class="fab fa-facebook me-2"></i>
                                تسجيل الدخول بـ Facebook
                            </button>
                        </div>

                        <div class="login-footer text-center mt-4">
                            <p class="mb-0">
                                ليس لديك حساب؟ 
                                <a href="?page=register" class="register-link">إنشاء حساب جديد</a>
                            </p>
                        </div>
                    </div>

                    <!-- معلومات إضافية -->
                    <div class="login-info mt-4">
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="info-item">
                                    <i class="fas fa-shield-alt fa-2x text-success mb-2"></i>
                                    <h6>أمان عالي</h6>
                                    <small class="text-muted">حماية متقدمة لبياناتك</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="info-item">
                                    <i class="fas fa-headset fa-2x text-primary mb-2"></i>
                                    <h6>دعم 24/7</h6>
                                    <small class="text-muted">فريق دعم متاح دائماً</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="info-item">
                                    <i class="fas fa-rocket fa-2x text-warning mb-2"></i>
                                    <h6>أداء سريع</h6>
                                    <small class="text-muted">خدمات عالية الجودة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<style>
.login-page {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.login-card {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    border: none;
}

.login-title {
    color: var(--primary-color);
    font-weight: 700;
    margin-bottom: 10px;
}

.login-subtitle {
    font-size: 1.1rem;
}

.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
}

.password-input-group {
    position: relative;
}

.password-toggle {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    z-index: 10;
}

.password-toggle:hover {
    color: var(--primary-color);
}

.login-btn {
    border-radius: 10px;
    padding: 12px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(44, 90, 160, 0.3);
}

.login-divider {
    text-align: center;
    margin: 30px 0;
    position: relative;
}

.login-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e9ecef;
}

.login-divider span {
    background: white;
    padding: 0 20px;
    color: #6c757d;
    font-weight: 500;
}

.social-btn {
    border-radius: 10px;
    padding: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.social-btn:hover {
    transform: translateY(-2px);
}

.forgot-password-link,
.register-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.forgot-password-link:hover,
.register-link:hover {
    color: var(--accent-color);
    text-decoration: underline;
}

.login-info {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 25px rgba(0,0,0,0.05);
}

.info-item {
    padding: 20px 10px;
}

.info-item h6 {
    color: var(--text-dark);
    font-weight: 600;
    margin-bottom: 5px;
}

/* تأثيرات متحركة */
.login-card {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* التجاوب مع الشاشات الصغيرة */
@media (max-width: 768px) {
    .login-card {
        padding: 30px 20px;
        margin: 20px;
    }
    
    .login-info {
        margin: 20px;
        padding: 20px;
    }
    
    .info-item {
        padding: 15px 5px;
    }
}
</style>

<script>
// تبديل إظهار/إخفاء كلمة المرور
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const toggle = input.nextElementSibling;
    const icon = toggle.querySelector('i');
    
    if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// التحقق من صحة النموذج
(function() {
    'use strict';
    window.addEventListener('load', function() {
        const forms = document.getElementsByClassName('needs-validation');
        Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// تأثيرات إضافية
document.addEventListener('DOMContentLoaded', function() {
    // تركيز تلقائي على حقل البريد الإلكتروني
    document.getElementById('email').focus();
    
    // تأثير الكتابة على العناصر
    const inputs = document.querySelectorAll('.form-control');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            if (!this.value) {
                this.parentElement.classList.remove('focused');
            }
        });
    });
});
</script>

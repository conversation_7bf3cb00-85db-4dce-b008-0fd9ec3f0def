<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// تسجيل النشاط قبل تسجيل الخروج
if (isLoggedIn()) {
    logActivity($_SESSION['user_id'], 'تسجيل الخروج', 'تم تسجيل الخروج بنجاح');
    
    // حذف رمز التذكر من قاعدة البيانات
    executeQuery("UPDATE users SET remember_token = NULL WHERE id = ?", [$_SESSION['user_id']]);
}

// حذف كوكي التذكر
if (isset($_COOKIE['remember_token'])) {
    setcookie('remember_token', '', time() - 3600, '/');
}

// تدمير الجلسة
session_destroy();

// حذف جميع الكوكيز المتعلقة بالجلسة
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// إعادة التوجيه إلى الصفحة الرئيسية مع رسالة نجاح
header("Location: index.php?message=logout_success");
exit();
?>

<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// تحديد الصفحة المطلوبة
$page = isset($_GET['page']) ? $_GET['page'] : 'home';

// قائمة الصفحات المسموحة
$allowed_pages = ['home', 'hosting', 'domains', 'vps', 'dedicated', 'about', 'contact', 'login', 'register', 'dashboard'];

if (!in_array($page, $allowed_pages)) {
    $page = 'home';
}

// تحديد عنوان الصفحة
$page_titles = [
    'home' => 'نقرة هوست - خدمات الاستضافة والسيرفرات',
    'hosting' => 'خدمات الاستضافة - نقرة هوست',
    'domains' => 'حجز النطاقات - نقرة هوست',
    'vps' => 'السيرفرات الافتراضية - نقرة هوست',
    'dedicated' => 'السيرفرات المخصصة - نقرة هوست',
    'about' => 'من نحن - نقرة هوست',
    'contact' => 'اتصل بنا - نقرة هوست',
    'login' => 'تسجيل الدخول - نقرة هوست',
    'register' => 'إنشاء حساب - نقرة هوست',
    'dashboard' => 'لوحة التحكم - نقرة هوست'
];

$page_title = $page_titles[$page];
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="assets/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/responsive.css">
    
    <!-- Google Fonts - خط عربي احترافي -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
    
    <!-- Meta Tags -->
    <meta name="description" content="نقرة هوست - أفضل خدمات الاستضافة والسيرفرات في الشرق الأوسط. خدمات موثوقة وسريعة مع دعم فني 24/7">
    <meta name="keywords" content="استضافة, سيرفرات, نطاقات, VPS, استضافة مواقع, نقرة هوست">
    <meta name="author" content="نقرة للتسويق الإلكتروني">
    
    <!-- Open Graph -->
    <meta property="og:title" content="<?php echo $page_title; ?>">
    <meta property="og:description" content="نقرة هوست - أفضل خدمات الاستضافة والسيرفرات">
    <meta property="og:image" content="https://nakra.host/assets/images/og-image.jpg">
    <meta property="og:url" content="https://nakra.host">
    <meta property="og:type" content="website">
</head>
<body>
    <!-- Header -->
    <?php include 'includes/header.php'; ?>
    
    <!-- Main Content -->
    <main>
        <?php
        $page_file = "pages/{$page}.php";
        if (file_exists($page_file)) {
            include $page_file;
        } else {
            include 'pages/404.php';
        }
        ?>
    </main>
    
    <!-- Footer -->
    <?php include 'includes/footer.php'; ?>
    
    <!-- JavaScript Files -->
    <script src="assets/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/jquery-3.6.0.min.js"></script>
    <script src="assets/js/main.js"></script>
    
    <!-- Live Chat Widget -->
    <div id="live-chat" class="live-chat-widget">
        <div class="chat-button" onclick="toggleChat()">
            <i class="fas fa-comments"></i>
            <span>دردشة مباشرة</span>
        </div>
    </div>
</body>
</html>

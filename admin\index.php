<?php
session_start();
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || !isAdmin()) {
    redirect('../index.php?page=login');
}

// تحديد الصفحة المطلوبة
$page = isset($_GET['page']) ? $_GET['page'] : 'dashboard';

// قائمة الصفحات المسموحة للإدارة
$allowed_pages = [
    'dashboard', 'users', 'orders', 'services', 'invoices', 'payments', 
    'support', 'settings', 'reports', 'domains', 'notifications'
];

if (!in_array($page, $allowed_pages)) {
    $page = 'dashboard';
}

// عناوين الصفحات
$page_titles = [
    'dashboard' => 'لوحة التحكم الرئيسية',
    'users' => 'إدارة العملاء',
    'orders' => 'إدارة الطلبات',
    'services' => 'إدارة الخدمات',
    'invoices' => 'إدارة الفواتير',
    'payments' => 'إدارة المدفوعات',
    'support' => 'إدارة الدعم الفني',
    'settings' => 'إعدادات النظام',
    'reports' => 'التقارير والإحصائيات',
    'domains' => 'إدارة النطاقات',
    'notifications' => 'إدارة الإشعارات'
];

$page_title = $page_titles[$page] ?? 'لوحة التحكم';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo SITE_NAME; ?> Admin</title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="../assets/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="assets/css/admin.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- DataTables -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css">
</head>
<body class="admin-body">
    <!-- Sidebar -->
    <nav class="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-brand">
                <img src="../assets/images/logo-white.png" alt="<?php echo SITE_NAME; ?>" height="30">
                <span class="brand-text"><?php echo SITE_NAME; ?></span>
            </div>
            <button class="sidebar-toggle" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </button>
        </div>
        
        <div class="sidebar-menu">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link <?php echo $page == 'dashboard' ? 'active' : ''; ?>" href="?page=dashboard">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>لوحة التحكم</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link <?php echo $page == 'users' ? 'active' : ''; ?>" href="?page=users">
                        <i class="fas fa-users"></i>
                        <span>العملاء</span>
                        <span class="badge bg-primary">
                            <?php echo fetchOne("SELECT COUNT(*) as count FROM users WHERE role = 'customer'")['count']; ?>
                        </span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link <?php echo $page == 'orders' ? 'active' : ''; ?>" href="?page=orders">
                        <i class="fas fa-shopping-cart"></i>
                        <span>الطلبات</span>
                        <span class="badge bg-warning">
                            <?php echo fetchOne("SELECT COUNT(*) as count FROM orders WHERE status = 'pending'")['count']; ?>
                        </span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link <?php echo $page == 'services' ? 'active' : ''; ?>" href="?page=services">
                        <i class="fas fa-server"></i>
                        <span>الخدمات</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link <?php echo $page == 'invoices' ? 'active' : ''; ?>" href="?page=invoices">
                        <i class="fas fa-file-invoice"></i>
                        <span>الفواتير</span>
                        <span class="badge bg-danger">
                            <?php echo fetchOne("SELECT COUNT(*) as count FROM invoices WHERE status = 'overdue'")['count']; ?>
                        </span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link <?php echo $page == 'payments' ? 'active' : ''; ?>" href="?page=payments">
                        <i class="fas fa-credit-card"></i>
                        <span>المدفوعات</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link <?php echo $page == 'support' ? 'active' : ''; ?>" href="?page=support">
                        <i class="fas fa-headset"></i>
                        <span>الدعم الفني</span>
                        <span class="badge bg-info">
                            <?php echo fetchOne("SELECT COUNT(*) as count FROM support_tickets WHERE status = 'open'")['count']; ?>
                        </span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link <?php echo $page == 'domains' ? 'active' : ''; ?>" href="?page=domains">
                        <i class="fas fa-globe"></i>
                        <span>النطاقات</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link <?php echo $page == 'reports' ? 'active' : ''; ?>" href="?page=reports">
                        <i class="fas fa-chart-bar"></i>
                        <span>التقارير</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link <?php echo $page == 'notifications' ? 'active' : ''; ?>" href="?page=notifications">
                        <i class="fas fa-bell"></i>
                        <span>الإشعارات</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link <?php echo $page == 'settings' ? 'active' : ''; ?>" href="?page=settings">
                        <i class="fas fa-cogs"></i>
                        <span>الإعدادات</span>
                    </a>
                </li>
            </ul>
        </div>
        
        <div class="sidebar-footer">
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user-shield"></i>
                </div>
                <div class="user-details">
                    <div class="user-name"><?php echo $_SESSION['user_name'] ?? 'المدير'; ?></div>
                    <div class="user-role">مدير النظام</div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Top Navigation -->
        <nav class="top-navbar">
            <div class="navbar-content">
                <div class="navbar-left">
                    <button class="btn btn-link sidebar-toggle-btn" id="sidebarToggleBtn">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h4 class="page-title mb-0"><?php echo $page_title; ?></h4>
                </div>
                
                <div class="navbar-right">
                    <!-- إشعارات سريعة -->
                    <div class="dropdown me-3">
                        <button class="btn btn-link position-relative" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-bell fa-lg"></i>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                <?php echo fetchOne("SELECT COUNT(*) as count FROM notifications WHERE is_read = 0")['count']; ?>
                            </span>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end notification-dropdown">
                            <li><h6 class="dropdown-header">الإشعارات الحديثة</h6></li>
                            <?php
                            $recent_notifications = fetchAll("SELECT * FROM notifications WHERE is_read = 0 ORDER BY created_at DESC LIMIT 5");
                            foreach ($recent_notifications as $notification):
                            ?>
                            <li>
                                <a class="dropdown-item" href="#">
                                    <div class="notification-item">
                                        <div class="notification-title"><?php echo htmlspecialchars($notification['title']); ?></div>
                                        <div class="notification-time"><?php echo formatArabicDate($notification['created_at']); ?></div>
                                    </div>
                                </a>
                            </li>
                            <?php endforeach; ?>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-center" href="?page=notifications">عرض جميع الإشعارات</a></li>
                        </ul>
                    </div>
                    
                    <!-- قائمة المستخدم -->
                    <div class="dropdown">
                        <button class="btn btn-link dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle fa-lg me-1"></i>
                            <?php echo $_SESSION['user_name'] ?? 'المدير'; ?>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="?page=profile">
                                <i class="fas fa-user me-2"></i>الملف الشخصي
                            </a></li>
                            <li><a class="dropdown-item" href="?page=settings">
                                <i class="fas fa-cogs me-2"></i>الإعدادات
                            </a></li>
                            <li><a class="dropdown-item" href="../index.php" target="_blank">
                                <i class="fas fa-external-link-alt me-2"></i>عرض الموقع
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="../logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Page Content -->
        <div class="page-content">
            <?php displayMessages(); ?>
            
            <?php
            $page_file = "pages/{$page}.php";
            if (file_exists($page_file)) {
                include $page_file;
            } else {
                echo '<div class="alert alert-danger">الصفحة المطلوبة غير موجودة.</div>';
            }
            ?>
        </div>
    </main>

    <!-- JavaScript Files -->
    <script src="../assets/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>
    <script src="assets/js/admin.js"></script>
    
    <script>
    // تبديل الشريط الجانبي
    document.getElementById('sidebarToggleBtn').addEventListener('click', function() {
        document.body.classList.toggle('sidebar-collapsed');
    });
    
    // تهيئة DataTables
    $(document).ready(function() {
        $('.data-table').DataTable({
            language: {
                url: 'https://cdn.datatables.net/plug-ins/1.13.4/i18n/ar.json'
            },
            responsive: true,
            pageLength: 25,
            order: [[0, 'desc']]
        });
    });
    </script>
</body>
</html>

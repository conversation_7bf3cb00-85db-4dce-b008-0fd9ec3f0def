<!-- شريط علوي للمعلومات -->
<div class="top-bar bg-primary text-white py-2 d-none d-md-block">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-6">
                <div class="d-flex align-items-center">
                    <i class="fas fa-phone-alt me-2"></i>
                    <span class="me-3"><?php echo SITE_PHONE; ?></span>
                    <i class="fas fa-envelope me-2"></i>
                    <span><?php echo SITE_EMAIL; ?></span>
                </div>
            </div>
            <div class="col-md-6 text-end">
                <div class="d-flex align-items-center justify-content-end">
                    <span class="me-3">مرحباً بكم في <?php echo SITE_NAME; ?></span>
                    <?php if (isLoggedIn()): ?>
                        <a href="?page=dashboard" class="text-white text-decoration-none me-2">
                            <i class="fas fa-user me-1"></i>
                            لوحة التحكم
                        </a>
                        <a href="logout.php" class="text-white text-decoration-none">
                            <i class="fas fa-sign-out-alt me-1"></i>
                            تسجيل الخروج
                        </a>
                    <?php else: ?>
                        <a href="?page=login" class="text-white text-decoration-none me-2">
                            <i class="fas fa-sign-in-alt me-1"></i>
                            تسجيل الدخول
                        </a>
                        <a href="?page=register" class="text-white text-decoration-none">
                            <i class="fas fa-user-plus me-1"></i>
                            إنشاء حساب
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- شريط التنقل الرئيسي -->
<nav class="navbar navbar-expand-lg navbar-light sticky-top">
    <div class="container">
        <!-- شعار الموقع -->
        <a class="navbar-brand d-flex align-items-center" href="index.php">
            <img src="assets/images/logo.png" alt="<?php echo SITE_NAME; ?>" height="40" class="me-2">
            <span class="fw-bold"><?php echo SITE_NAME; ?></span>
        </a>

        <!-- زر القائمة للشاشات الصغيرة -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>

        <!-- قائمة التنقل -->
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link <?php echo ($page == 'home') ? 'active' : ''; ?>" href="index.php">
                        <i class="fas fa-home me-1"></i>
                        الرئيسية
                    </a>
                </li>
                
                <!-- قائمة الاستضافة -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle <?php echo in_array($page, ['hosting', 'vps', 'dedicated']) ? 'active' : ''; ?>" 
                       href="#" id="hostingDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-server me-1"></i>
                        خدمات الاستضافة
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="?page=hosting">
                            <i class="fas fa-share-alt me-2"></i>الاستضافة المشتركة
                        </a></li>
                        <li><a class="dropdown-item" href="?page=vps">
                            <i class="fas fa-cloud me-2"></i>السيرفرات الافتراضية
                        </a></li>
                        <li><a class="dropdown-item" href="?page=dedicated">
                            <i class="fas fa-server me-2"></i>السيرفرات المخصصة
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="?page=hosting&type=wordpress">
                            <i class="fab fa-wordpress me-2"></i>استضافة ووردبريس
                        </a></li>
                    </ul>
                </li>

                <li class="nav-item">
                    <a class="nav-link <?php echo ($page == 'domains') ? 'active' : ''; ?>" href="?page=domains">
                        <i class="fas fa-globe me-1"></i>
                        النطاقات
                    </a>
                </li>

                <!-- قائمة الخدمات الإضافية -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="servicesDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-cogs me-1"></i>
                        خدمات إضافية
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="?page=ssl">
                            <i class="fas fa-shield-alt me-2"></i>شهادات SSL
                        </a></li>
                        <li><a class="dropdown-item" href="?page=backup">
                            <i class="fas fa-save me-2"></i>النسخ الاحتياطي
                        </a></li>
                        <li><a class="dropdown-item" href="?page=email">
                            <i class="fas fa-envelope me-2"></i>البريد الإلكتروني
                        </a></li>
                        <li><a class="dropdown-item" href="?page=cdn">
                            <i class="fas fa-network-wired me-2"></i>شبكة CDN
                        </a></li>
                    </ul>
                </li>

                <li class="nav-item">
                    <a class="nav-link <?php echo ($page == 'about') ? 'active' : ''; ?>" href="?page=about">
                        <i class="fas fa-info-circle me-1"></i>
                        من نحن
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link <?php echo ($page == 'contact') ? 'active' : ''; ?>" href="?page=contact">
                        <i class="fas fa-phone me-1"></i>
                        اتصل بنا
                    </a>
                </li>
            </ul>

            <!-- أزرار العمل -->
            <div class="d-flex align-items-center">
                <?php if (isLoggedIn()): ?>
                    <!-- إشعارات العميل -->
                    <div class="dropdown me-3">
                        <button class="btn btn-outline-primary position-relative" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-bell"></i>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                3
                            </span>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><h6 class="dropdown-header">الإشعارات</h6></li>
                            <li><a class="dropdown-item" href="#">
                                <small class="text-muted">منذ ساعة</small><br>
                                تم تجديد خدمة الاستضافة بنجاح
                            </a></li>
                            <li><a class="dropdown-item" href="#">
                                <small class="text-muted">منذ يومين</small><br>
                                فاتورة جديدة متاحة للدفع
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-center" href="?page=notifications">عرض جميع الإشعارات</a></li>
                        </ul>
                    </div>

                    <!-- قائمة المستخدم -->
                    <div class="dropdown">
                        <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            <?php echo $_SESSION['user_name'] ?? 'المستخدم'; ?>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="?page=dashboard">
                                <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                            </a></li>
                            <li><a class="dropdown-item" href="?page=profile">
                                <i class="fas fa-user-edit me-2"></i>الملف الشخصي
                            </a></li>
                            <li><a class="dropdown-item" href="?page=orders">
                                <i class="fas fa-shopping-cart me-2"></i>طلباتي
                            </a></li>
                            <li><a class="dropdown-item" href="?page=invoices">
                                <i class="fas fa-file-invoice me-2"></i>الفواتير
                            </a></li>
                            <li><a class="dropdown-item" href="?page=support">
                                <i class="fas fa-headset me-2"></i>الدعم الفني
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                            </a></li>
                        </ul>
                    </div>
                <?php else: ?>
                    <a href="?page=login" class="btn btn-outline-primary me-2">
                        <i class="fas fa-sign-in-alt me-1"></i>
                        تسجيل الدخول
                    </a>
                    <a href="?page=register" class="btn btn-primary">
                        <i class="fas fa-user-plus me-1"></i>
                        إنشاء حساب
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</nav>

<!-- شريط الإعلانات (اختياري) -->
<?php if (isset($_SESSION['announcement']) && !empty($_SESSION['announcement'])): ?>
<div class="alert alert-info alert-dismissible fade show m-0 rounded-0 text-center" role="alert">
    <strong>إعلان هام:</strong> <?php echo $_SESSION['announcement']; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- عرض الرسائل -->
<div class="container mt-3">
    <?php displayMessages(); ?>
</div>

<script>
// تأثير التمرير على شريط التنقل
window.addEventListener('scroll', function() {
    const navbar = document.querySelector('.navbar');
    if (window.scrollY > 100) {
        navbar.classList.add('scrolled');
    } else {
        navbar.classList.remove('scrolled');
    }
});

// إغلاق القوائم المنسدلة عند النقر خارجها
document.addEventListener('click', function(e) {
    const dropdowns = document.querySelectorAll('.dropdown-menu.show');
    dropdowns.forEach(dropdown => {
        if (!dropdown.parentElement.contains(e.target)) {
            dropdown.classList.remove('show');
        }
    });
});
</script>

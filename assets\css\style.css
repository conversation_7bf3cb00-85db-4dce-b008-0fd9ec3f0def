/* ===== متغيرات الألوان الهادئة ===== */
:root {
    --primary-color: #2c5aa0;
    --secondary-color: #34495e;
    --accent-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --light-bg: #f8f9fa;
    --white: #ffffff;
    --text-dark: #2c3e50;
    --text-light: #7f8c8d;
    --border-color: #e9ecef;
    --shadow: 0 2px 10px rgba(0,0,0,0.1);
    --gradient: linear-gradient(135deg, var(--primary-color), var(--accent-color));
}

/* ===== الخط العربي الاحترافي ===== */
* {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    background-color: var(--light-bg);
    direction: rtl;
    text-align: right;
}

/* ===== التخطيط العام ===== */
.container-fluid {
    padding: 0;
}

.section-padding {
    padding: 80px 0;
}

.section-title {
    text-align: center;
    margin-bottom: 50px;
}

.section-title h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 15px;
}

.section-title p {
    font-size: 1.1rem;
    color: var(--text-light);
    max-width: 600px;
    margin: 0 auto;
}

/* ===== الهيدر ===== */
.navbar {
    background: var(--white);
    box-shadow: var(--shadow);
    padding: 15px 0;
    transition: all 0.3s ease;
}

.navbar.scrolled {
    padding: 10px 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

.navbar-brand {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary-color);
    text-decoration: none;
}

.navbar-nav .nav-link {
    color: var(--text-dark);
    font-weight: 500;
    margin: 0 10px;
    padding: 10px 15px;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: var(--white);
    background: var(--gradient);
    transform: translateY(-2px);
}

/* ===== الأزرار ===== */
.btn {
    border-radius: 25px;
    padding: 12px 30px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    border: none;
}

.btn-primary {
    background: var(--gradient);
    color: var(--white);
    box-shadow: 0 4px 15px rgba(44, 90, 160, 0.3);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(44, 90, 160, 0.4);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
}

.btn-success {
    background: var(--success-color);
    color: var(--white);
}

.btn-warning {
    background: var(--warning-color);
    color: var(--white);
}

.btn-danger {
    background: var(--danger-color);
    color: var(--white);
}

/* ===== البطاقات ===== */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
}

.card-header {
    background: var(--gradient);
    color: var(--white);
    border: none;
    padding: 20px;
    text-align: center;
}

.card-body {
    padding: 30px;
}

.card-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 15px;
}

.card-text {
    color: var(--text-light);
    line-height: 1.8;
}

/* ===== خطط الاستضافة ===== */
.pricing-card {
    position: relative;
    background: var(--white);
    border-radius: 20px;
    padding: 40px 30px;
    text-align: center;
    transition: all 0.3s ease;
    border: 2px solid var(--border-color);
}

.pricing-card.featured {
    border-color: var(--primary-color);
    transform: scale(1.05);
}

.pricing-card.featured::before {
    content: 'الأكثر شعبية';
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gradient);
    color: var(--white);
    padding: 8px 25px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.pricing-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

.pricing-card .price {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-color);
    margin: 20px 0;
}

.pricing-card .price .currency {
    font-size: 1.2rem;
    color: var(--text-light);
}

.pricing-card .features {
    list-style: none;
    padding: 0;
    margin: 30px 0;
}

.pricing-card .features li {
    padding: 10px 0;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-dark);
}

.pricing-card .features li:last-child {
    border-bottom: none;
}

.pricing-card .features li i {
    color: var(--success-color);
    margin-left: 10px;
}

/* ===== الفوتر ===== */
.footer {
    background: var(--secondary-color);
    color: var(--white);
    padding: 60px 0 20px;
}

.footer h5 {
    color: var(--white);
    font-weight: 700;
    margin-bottom: 20px;
}

.footer ul {
    list-style: none;
    padding: 0;
}

.footer ul li {
    margin-bottom: 10px;
}

.footer ul li a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer ul li a:hover {
    color: var(--white);
}

.footer-bottom {
    border-top: 1px solid #4a5568;
    margin-top: 40px;
    padding-top: 20px;
    text-align: center;
    color: #bdc3c7;
}

/* ===== الدردشة المباشرة ===== */
.live-chat-widget {
    position: fixed;
    bottom: 20px;
    left: 20px;
    z-index: 1000;
}

.chat-button {
    background: var(--gradient);
    color: var(--white);
    padding: 15px 20px;
    border-radius: 50px;
    cursor: pointer;
    box-shadow: 0 4px 15px rgba(44, 90, 160, 0.3);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
}

.chat-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(44, 90, 160, 0.4);
}

/* ===== الرسائل ===== */
.alert {
    border-radius: 10px;
    border: none;
    padding: 15px 20px;
    margin-bottom: 20px;
}

.alert-success {
    background: rgba(39, 174, 96, 0.1);
    color: var(--success-color);
    border-right: 4px solid var(--success-color);
}

.alert-danger {
    background: rgba(231, 76, 60, 0.1);
    color: var(--danger-color);
    border-right: 4px solid var(--danger-color);
}

.alert-warning {
    background: rgba(243, 156, 18, 0.1);
    color: var(--warning-color);
    border-right: 4px solid var(--warning-color);
}

.alert-info {
    background: rgba(52, 152, 219, 0.1);
    color: var(--accent-color);
    border-right: 4px solid var(--accent-color);
}

/* ===== النماذج ===== */
.form-control {
    border-radius: 10px;
    border: 2px solid var(--border-color);
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 8px;
}

/* ===== الجداول ===== */
.table {
    background: var(--white);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow);
}

.table thead th {
    background: var(--gradient);
    color: var(--white);
    border: none;
    font-weight: 600;
    padding: 15px;
}

.table tbody td {
    padding: 15px;
    border-color: var(--border-color);
    vertical-align: middle;
}

.table tbody tr:hover {
    background: rgba(44, 90, 160, 0.05);
}

/* ===== الحالات ===== */
.status-badge {
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-active {
    background: rgba(39, 174, 96, 0.1);
    color: var(--success-color);
}

.status-pending {
    background: rgba(243, 156, 18, 0.1);
    color: var(--warning-color);
}

.status-suspended {
    background: rgba(231, 76, 60, 0.1);
    color: var(--danger-color);
}

.status-expired {
    background: rgba(149, 165, 166, 0.1);
    color: #95a5a6;
}

/* ===== الرسوم المتحركة ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

/* ===== التحسينات للشاشات الصغيرة ===== */
@media (max-width: 768px) {
    .section-padding {
        padding: 40px 0;
    }
    
    .section-title h2 {
        font-size: 2rem;
    }
    
    .pricing-card.featured {
        transform: none;
        margin-bottom: 30px;
    }
    
    .navbar-nav {
        text-align: center;
        margin-top: 20px;
    }
    
    .live-chat-widget {
        bottom: 10px;
        left: 10px;
    }
    
    .chat-button span {
        display: none;
    }
}

<?php
// جلب الإحصائيات الأساسية
$total_users = fetchOne("SELECT COUNT(*) as count FROM users WHERE role = 'customer'")['count'];
$total_orders = fetchOne("SELECT COUNT(*) as count FROM orders")['count'];
$active_orders = fetchOne("SELECT COUNT(*) as count FROM orders WHERE status = 'active'")['count'];
$pending_orders = fetchOne("SELECT COUNT(*) as count FROM orders WHERE status = 'pending'")['count'];
$total_revenue = fetchOne("SELECT COALESCE(SUM(total_amount), 0) as total FROM payments WHERE status = 'completed'")['total'];
$monthly_revenue = fetchOne("SELECT COALESCE(SUM(total_amount), 0) as total FROM payments WHERE status = 'completed' AND MONTH(created_at) = MONTH(CURRENT_DATE()) AND YEAR(created_at) = YEAR(CURRENT_DATE())")['total'];
$open_tickets = fetchOne("SELECT COUNT(*) as count FROM support_tickets WHERE status IN ('open', 'in_progress')")['count'];
$overdue_invoices = fetchOne("SELECT COUNT(*) as count FROM invoices WHERE status = 'overdue'")['count'];

// إحصائيات الشهر الماضي للمقارنة
$last_month_revenue = fetchOne("SELECT COALESCE(SUM(total_amount), 0) as total FROM payments WHERE status = 'completed' AND MONTH(created_at) = MONTH(CURRENT_DATE() - INTERVAL 1 MONTH) AND YEAR(created_at) = YEAR(CURRENT_DATE() - INTERVAL 1 MONTH)")['total'];
$revenue_change = $last_month_revenue > 0 ? (($monthly_revenue - $last_month_revenue) / $last_month_revenue) * 100 : 0;

// الطلبات الحديثة
$recent_orders = fetchAll("
    SELECT o.*, u.first_name, u.last_name, s.name as service_name 
    FROM orders o 
    JOIN users u ON o.user_id = u.id 
    JOIN services s ON o.service_id = s.id 
    ORDER BY o.created_at DESC 
    LIMIT 5
");

// التذاكر الحديثة
$recent_tickets = fetchAll("
    SELECT st.*, u.first_name, u.last_name 
    FROM support_tickets st 
    JOIN users u ON st.user_id = u.id 
    ORDER BY st.created_at DESC 
    LIMIT 5
");
?>

<!-- لوحة التحكم الرئيسية -->
<div class="dashboard-page">
    <!-- الإجراءات السريعة -->
    <div class="quick-actions mb-4">
        <a href="?page=orders&action=add" class="quick-action-btn">
            <i class="fas fa-plus quick-action-icon"></i>
            <div class="quick-action-title">طلب جديد</div>
            <div class="quick-action-desc">إضافة طلب جديد للعميل</div>
        </a>
        <a href="?page=users&action=add" class="quick-action-btn">
            <i class="fas fa-user-plus quick-action-icon"></i>
            <div class="quick-action-title">عميل جديد</div>
            <div class="quick-action-desc">إضافة عميل جديد</div>
        </a>
        <a href="?page=invoices&action=add" class="quick-action-btn">
            <i class="fas fa-file-invoice quick-action-icon"></i>
            <div class="quick-action-title">فاتورة جديدة</div>
            <div class="quick-action-desc">إنشاء فاتورة جديدة</div>
        </a>
        <a href="?page=support" class="quick-action-btn">
            <i class="fas fa-headset quick-action-icon"></i>
            <div class="quick-action-title">الدعم الفني</div>
            <div class="quick-action-desc">إدارة تذاكر الدعم</div>
        </a>
    </div>

    <!-- البطاقات الإحصائية -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon primary">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stats-number"><?php echo number_format($total_users); ?></div>
                <div class="stats-label">إجمالي العملاء</div>
                <div class="stats-change positive">
                    <i class="fas fa-arrow-up me-1"></i>
                    +12% من الشهر الماضي
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card success">
                <div class="stats-icon success">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="stats-number"><?php echo number_format($active_orders); ?></div>
                <div class="stats-label">الطلبات النشطة</div>
                <div class="stats-change positive">
                    <i class="fas fa-arrow-up me-1"></i>
                    +8% من الشهر الماضي
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card warning">
                <div class="stats-icon warning">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stats-number"><?php echo number_format($pending_orders); ?></div>
                <div class="stats-label">طلبات في الانتظار</div>
                <div class="stats-change">
                    <i class="fas fa-minus me-1"></i>
                    بدون تغيير
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card info">
                <div class="stats-icon info">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <div class="stats-number"><?php echo number_format($monthly_revenue, 0); ?></div>
                <div class="stats-label">إيرادات الشهر (جنيه)</div>
                <div class="stats-change <?php echo $revenue_change >= 0 ? 'positive' : 'negative'; ?>">
                    <i class="fas fa-arrow-<?php echo $revenue_change >= 0 ? 'up' : 'down'; ?> me-1"></i>
                    <?php echo abs(round($revenue_change, 1)); ?>% من الشهر الماضي
                </div>
            </div>
        </div>
    </div>

    <!-- الرسوم البيانية والتقارير -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="chart-container">
                <h5 class="chart-title">إيرادات آخر 12 شهر</h5>
                <canvas id="revenueChart" height="100"></canvas>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="chart-container">
                <h5 class="chart-title">توزيع الطلبات حسب الحالة</h5>
                <canvas id="ordersChart" height="200"></canvas>
            </div>
        </div>
    </div>

    <!-- الجداول -->
    <div class="row">
        <div class="col-lg-6">
            <div class="admin-card">
                <div class="admin-card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-shopping-cart me-2"></i>
                        الطلبات الحديثة
                    </h5>
                </div>
                <div class="admin-card-body">
                    <?php if ($recent_orders): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>العميل</th>
                                        <th>الخدمة</th>
                                        <th>المبلغ</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_orders as $order): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($order['first_name'] . ' ' . $order['last_name']); ?></strong>
                                            </td>
                                            <td><?php echo htmlspecialchars($order['service_name']); ?></td>
                                            <td><?php echo number_format($order['total_amount'], 2); ?> جنيه</td>
                                            <td>
                                                <span class="status-badge status-<?php echo $order['status']; ?>">
                                                    <?php
                                                    $statuses = [
                                                        'pending' => 'في الانتظار',
                                                        'processing' => 'قيد المعالجة',
                                                        'active' => 'نشط',
                                                        'suspended' => 'معلق',
                                                        'cancelled' => 'ملغي'
                                                    ];
                                                    echo $statuses[$order['status']] ?? $order['status'];
                                                    ?>
                                                </span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <div class="text-center mt-3">
                            <a href="?page=orders" class="btn btn-outline-primary">عرض جميع الطلبات</a>
                        </div>
                    <?php else: ?>
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                            <p>لا توجد طلبات حديثة</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="admin-card">
                <div class="admin-card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-headset me-2"></i>
                        تذاكر الدعم الحديثة
                    </h5>
                </div>
                <div class="admin-card-body">
                    <?php if ($recent_tickets): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>العميل</th>
                                        <th>الموضوع</th>
                                        <th>الأولوية</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_tickets as $ticket): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($ticket['first_name'] . ' ' . $ticket['last_name']); ?></strong>
                                            </td>
                                            <td>
                                                <a href="?page=support&action=view&id=<?php echo $ticket['id']; ?>" class="text-decoration-none">
                                                    <?php echo htmlspecialchars(truncateText($ticket['subject'], 30)); ?>
                                                </a>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo $ticket['priority'] == 'high' ? 'danger' : ($ticket['priority'] == 'medium' ? 'warning' : 'info'); ?>">
                                                    <?php
                                                    $priorities = [
                                                        'low' => 'منخفضة',
                                                        'medium' => 'متوسطة',
                                                        'high' => 'عالية',
                                                        'urgent' => 'عاجلة'
                                                    ];
                                                    echo $priorities[$ticket['priority']] ?? $ticket['priority'];
                                                    ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="status-badge status-<?php echo $ticket['status'] == 'open' ? 'pending' : 'active'; ?>">
                                                    <?php
                                                    $ticket_statuses = [
                                                        'open' => 'مفتوحة',
                                                        'in_progress' => 'قيد المعالجة',
                                                        'waiting_customer' => 'في انتظار العميل',
                                                        'resolved' => 'محلولة',
                                                        'closed' => 'مغلقة'
                                                    ];
                                                    echo $ticket_statuses[$ticket['status']] ?? $ticket['status'];
                                                    ?>
                                                </span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <div class="text-center mt-3">
                            <a href="?page=support" class="btn btn-outline-primary">عرض جميع التذاكر</a>
                        </div>
                    <?php else: ?>
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-headset fa-3x mb-3"></i>
                            <p>لا توجد تذاكر دعم حديثة</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- تنبيهات مهمة -->
    <?php if ($overdue_invoices > 0 || $open_tickets > 0): ?>
        <div class="row mt-4">
            <div class="col-12">
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>تنبيهات مهمة</h6>
                    <ul class="mb-0">
                        <?php if ($overdue_invoices > 0): ?>
                            <li>يوجد <?php echo $overdue_invoices; ?> فاتورة متأخرة السداد</li>
                        <?php endif; ?>
                        <?php if ($open_tickets > 0): ?>
                            <li>يوجد <?php echo $open_tickets; ?> تذكرة دعم مفتوحة تحتاج للمتابعة</li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
// رسم بياني للإيرادات
const revenueCtx = document.getElementById('revenueChart').getContext('2d');
const revenueChart = new Chart(revenueCtx, {
    type: 'line',
    data: {
        labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
        datasets: [{
            label: 'الإيرادات (جنيه)',
            data: [12000, 15000, 18000, 22000, 25000, 28000, 32000, 35000, 38000, 42000, 45000, <?php echo $monthly_revenue; ?>],
            borderColor: '#2c5aa0',
            backgroundColor: 'rgba(44, 90, 160, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return value.toLocaleString() + ' جنيه';
                    }
                }
            }
        }
    }
});

// رسم بياني للطلبات
const ordersCtx = document.getElementById('ordersChart').getContext('2d');
const ordersChart = new Chart(ordersCtx, {
    type: 'doughnut',
    data: {
        labels: ['نشطة', 'في الانتظار', 'معلقة', 'ملغية'],
        datasets: [{
            data: [<?php echo $active_orders; ?>, <?php echo $pending_orders; ?>, 5, 3],
            backgroundColor: ['#27ae60', '#f39c12', '#e74c3c', '#95a5a6']
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
</script>

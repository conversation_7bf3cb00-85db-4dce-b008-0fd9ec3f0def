/* ===== متغيرات لوحة التحكم ===== */
:root {
    --admin-primary: #2c5aa0;
    --admin-secondary: #34495e;
    --admin-success: #27ae60;
    --admin-warning: #f39c12;
    --admin-danger: #e74c3c;
    --admin-info: #3498db;
    --admin-light: #f8f9fa;
    --admin-dark: #2c3e50;
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 70px;
    --topbar-height: 70px;
    --admin-shadow: 0 2px 10px rgba(0,0,0,0.1);
    --admin-border: #e9ecef;
}

/* ===== التخطيط العام ===== */
.admin-body {
    font-family: 'Cairo', sans-serif;
    background-color: var(--admin-light);
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

/* ===== الشريط الجانبي ===== */
.sidebar {
    position: fixed;
    top: 0;
    right: 0;
    width: var(--sidebar-width);
    height: 100vh;
    background: linear-gradient(180deg, var(--admin-primary), var(--admin-secondary));
    color: white;
    z-index: 1000;
    transition: all 0.3s ease;
    overflow-y: auto;
    box-shadow: var(--admin-shadow);
}

.sidebar-collapsed .sidebar {
    width: var(--sidebar-collapsed-width);
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sidebar-brand {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: white;
}

.brand-text {
    margin-right: 10px;
    font-size: 1.2rem;
    font-weight: 700;
    transition: opacity 0.3s ease;
}

.sidebar-collapsed .brand-text {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 5px;
    border-radius: 5px;
    transition: background-color 0.3s ease;
}

.sidebar-toggle:hover {
    background-color: rgba(255,255,255,0.1);
}

/* ===== قائمة الشريط الجانبي ===== */
.sidebar-menu {
    padding: 20px 0;
}

.sidebar-menu .nav-link {
    color: rgba(255,255,255,0.8);
    padding: 15px 20px;
    display: flex;
    align-items: center;
    text-decoration: none;
    transition: all 0.3s ease;
    border-radius: 0;
    position: relative;
}

.sidebar-menu .nav-link:hover {
    background-color: rgba(255,255,255,0.1);
    color: white;
    transform: translateX(-5px);
}

.sidebar-menu .nav-link.active {
    background-color: rgba(255,255,255,0.2);
    color: white;
    border-left: 4px solid white;
}

.sidebar-menu .nav-link i {
    width: 20px;
    margin-left: 15px;
    text-align: center;
    font-size: 1.1rem;
}

.sidebar-menu .nav-link span {
    transition: opacity 0.3s ease;
}

.sidebar-collapsed .sidebar-menu .nav-link span {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.sidebar-menu .badge {
    margin-right: auto;
    font-size: 0.75rem;
}

.sidebar-collapsed .sidebar-menu .badge {
    display: none;
}

/* ===== تذييل الشريط الجانبي ===== */
.sidebar-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20px;
    border-top: 1px solid rgba(255,255,255,0.1);
}

.user-info {
    display: flex;
    align-items: center;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background-color: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 10px;
}

.user-details {
    transition: opacity 0.3s ease;
}

.sidebar-collapsed .user-details {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.user-name {
    font-weight: 600;
    font-size: 0.9rem;
}

.user-role {
    font-size: 0.8rem;
    opacity: 0.8;
}

/* ===== المحتوى الرئيسي ===== */
.main-content {
    margin-right: var(--sidebar-width);
    min-height: 100vh;
    transition: margin-right 0.3s ease;
}

.sidebar-collapsed .main-content {
    margin-right: var(--sidebar-collapsed-width);
}

/* ===== شريط التنقل العلوي ===== */
.top-navbar {
    background: white;
    height: var(--topbar-height);
    box-shadow: var(--admin-shadow);
    position: sticky;
    top: 0;
    z-index: 999;
}

.navbar-content {
    height: 100%;
    padding: 0 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.navbar-left {
    display: flex;
    align-items: center;
}

.sidebar-toggle-btn {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: var(--admin-dark);
    margin-left: 15px;
    padding: 8px;
    border-radius: 5px;
    transition: background-color 0.3s ease;
}

.sidebar-toggle-btn:hover {
    background-color: var(--admin-light);
}

.page-title {
    color: var(--admin-dark);
    font-weight: 600;
}

.navbar-right {
    display: flex;
    align-items: center;
}

/* ===== قائمة الإشعارات ===== */
.notification-dropdown {
    width: 350px;
    max-height: 400px;
    overflow-y: auto;
}

.notification-item {
    padding: 10px 0;
}

.notification-title {
    font-weight: 600;
    font-size: 0.9rem;
    color: var(--admin-dark);
}

.notification-time {
    font-size: 0.8rem;
    color: var(--admin-secondary);
}

/* ===== محتوى الصفحة ===== */
.page-content {
    padding: 30px;
    min-height: calc(100vh - var(--topbar-height));
}

/* ===== البطاقات الإحصائية ===== */
.stats-card {
    background: white;
    border-radius: 10px;
    padding: 25px;
    box-shadow: var(--admin-shadow);
    transition: all 0.3s ease;
    border: none;
    position: relative;
    overflow: hidden;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--admin-primary);
}

.stats-card.success::before {
    background: var(--admin-success);
}

.stats-card.warning::before {
    background: var(--admin-warning);
}

.stats-card.danger::before {
    background: var(--admin-danger);
}

.stats-card.info::before {
    background: var(--admin-info);
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-bottom: 15px;
}

.stats-icon.primary {
    background: var(--admin-primary);
}

.stats-icon.success {
    background: var(--admin-success);
}

.stats-icon.warning {
    background: var(--admin-warning);
}

.stats-icon.danger {
    background: var(--admin-danger);
}

.stats-icon.info {
    background: var(--admin-info);
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--admin-dark);
    margin-bottom: 5px;
}

.stats-label {
    color: var(--admin-secondary);
    font-weight: 500;
    margin-bottom: 10px;
}

.stats-change {
    font-size: 0.9rem;
    display: flex;
    align-items: center;
}

.stats-change.positive {
    color: var(--admin-success);
}

.stats-change.negative {
    color: var(--admin-danger);
}

/* ===== الجداول ===== */
.admin-table {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--admin-shadow);
}

.admin-table .table {
    margin-bottom: 0;
}

.admin-table .table thead th {
    background: var(--admin-primary);
    color: white;
    border: none;
    font-weight: 600;
    padding: 15px;
}

.admin-table .table tbody td {
    padding: 15px;
    vertical-align: middle;
    border-color: var(--admin-border);
}

.admin-table .table tbody tr:hover {
    background-color: rgba(44, 90, 160, 0.05);
}

/* ===== الأزرار الإدارية ===== */
.btn-admin {
    border-radius: 8px;
    padding: 10px 20px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-admin:hover {
    transform: translateY(-2px);
}

/* ===== النماذج الإدارية ===== */
.admin-form {
    background: white;
    border-radius: 10px;
    padding: 30px;
    box-shadow: var(--admin-shadow);
}

.admin-form .form-control {
    border-radius: 8px;
    border: 2px solid var(--admin-border);
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.admin-form .form-control:focus {
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
}

.admin-form .form-label {
    font-weight: 600;
    color: var(--admin-dark);
    margin-bottom: 8px;
}

/* ===== الرسوم البيانية ===== */
.chart-container {
    background: white;
    border-radius: 10px;
    padding: 25px;
    box-shadow: var(--admin-shadow);
    margin-bottom: 30px;
}

.chart-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--admin-dark);
    margin-bottom: 20px;
}

/* ===== الحالات والشارات ===== */
.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-active {
    background: rgba(39, 174, 96, 0.1);
    color: var(--admin-success);
}

.status-pending {
    background: rgba(243, 156, 18, 0.1);
    color: var(--admin-warning);
}

.status-suspended {
    background: rgba(231, 76, 60, 0.1);
    color: var(--admin-danger);
}

.status-inactive {
    background: rgba(149, 165, 166, 0.1);
    color: #95a5a6;
}

/* ===== التجاوب مع الشاشات الصغيرة ===== */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-right: 0;
    }
    
    .navbar-content {
        padding: 0 15px;
    }
    
    .page-content {
        padding: 15px;
    }
    
    .stats-card {
        margin-bottom: 20px;
    }
    
    .notification-dropdown {
        width: 280px;
    }
}

/* ===== تأثيرات التحميل ===== */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* ===== تحسينات إضافية ===== */
.admin-card {
    background: white;
    border-radius: 10px;
    box-shadow: var(--admin-shadow);
    border: none;
    transition: all 0.3s ease;
}

.admin-card:hover {
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.admin-card-header {
    background: var(--admin-light);
    border-bottom: 1px solid var(--admin-border);
    padding: 20px;
    border-radius: 10px 10px 0 0;
}

.admin-card-body {
    padding: 25px;
}

.quick-actions {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    margin-bottom: 30px;
}

.quick-action-btn {
    flex: 1;
    min-width: 200px;
    padding: 20px;
    background: white;
    border: 2px solid var(--admin-border);
    border-radius: 10px;
    text-decoration: none;
    color: var(--admin-dark);
    transition: all 0.3s ease;
    text-align: center;
}

.quick-action-btn:hover {
    border-color: var(--admin-primary);
    color: var(--admin-primary);
    transform: translateY(-3px);
    box-shadow: var(--admin-shadow);
}

.quick-action-icon {
    font-size: 2rem;
    margin-bottom: 10px;
    display: block;
}

.quick-action-title {
    font-weight: 600;
    margin-bottom: 5px;
}

.quick-action-desc {
    font-size: 0.9rem;
    color: var(--admin-secondary);
}

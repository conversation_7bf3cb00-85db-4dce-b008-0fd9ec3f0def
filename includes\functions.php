<?php
require_once 'config/config.php';

// دالة لتحويل التاريخ إلى العربية
function formatArabicDate($date, $format = 'Y-m-d H:i:s') {
    $arabic_months = [
        1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
        5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
        9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
    ];
    
    $timestamp = is_string($date) ? strtotime($date) : $date;
    $day = date('d', $timestamp);
    $month = $arabic_months[(int)date('m', $timestamp)];
    $year = date('Y', $timestamp);
    $time = date('H:i', $timestamp);
    
    return "$day $month $year - $time";
}

// دالة لتحويل الأرقام إلى العربية
function convertToArabicNumbers($string) {
    $arabic_numbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    $english_numbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    
    return str_replace($english_numbers, $arabic_numbers, $string);
}

// دالة لتنسيق السعر
function formatPrice($price, $currency = 'جنيه') {
    return number_format($price, 2) . ' ' . $currency;
}

// دالة للتحقق من تسجيل الدخول
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

// دالة للتحقق من صلاحيات المدير
function isAdmin() {
    return isLoggedIn() && isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
}

// دالة لإعادة التوجيه
function redirect($url) {
    header("Location: $url");
    exit();
}

// دالة لعرض الرسائل
function showMessage($message, $type = 'success') {
    $_SESSION['message'] = $message;
    $_SESSION['message_type'] = $type;
}

// دالة لعرض الرسائل المحفوظة
function displayMessages() {
    if (isset($_SESSION['message'])) {
        $type = $_SESSION['message_type'] ?? 'success';
        $message = $_SESSION['message'];
        
        echo "<div class='alert alert-{$type} alert-dismissible fade show' role='alert'>
                {$message}
                <button type='button' class='btn-close' data-bs-dismiss='alert'></button>
              </div>";
        
        unset($_SESSION['message']);
        unset($_SESSION['message_type']);
    }
}

// دالة لتوليد CSRF Token
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = generateRandomToken();
    }
    return $_SESSION['csrf_token'];
}

// دالة للتحقق من CSRF Token
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// دالة لتسجيل الأحداث
function logActivity($user_id, $action, $details = '') {
    global $db;
    
    $query = "INSERT INTO activity_logs (user_id, action, details, ip_address, created_at) 
              VALUES (?, ?, ?, ?, NOW())";
    
    $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
    
    executeQuery($query, [$user_id, $action, $details, $ip_address]);
}

// دالة لإرسال البريد الإلكتروني
function sendEmail($to, $subject, $message, $from_name = null) {
    $from_name = $from_name ?? SITE_NAME;
    $from_email = SITE_EMAIL;
    
    $headers = [
        'MIME-Version: 1.0',
        'Content-type: text/html; charset=UTF-8',
        "From: {$from_name} <{$from_email}>",
        "Reply-To: {$from_email}",
        'X-Mailer: PHP/' . phpversion()
    ];
    
    return mail($to, $subject, $message, implode("\r\n", $headers));
}

// دالة لتحميل الملفات بأمان
function uploadFile($file, $allowed_types = ['jpg', 'jpeg', 'png', 'gif', 'pdf'], $max_size = 5242880) {
    if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
        return ['success' => false, 'message' => 'لم يتم اختيار ملف'];
    }
    
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    
    if (!in_array($file_extension, $allowed_types)) {
        return ['success' => false, 'message' => 'نوع الملف غير مسموح'];
    }
    
    if ($file['size'] > $max_size) {
        return ['success' => false, 'message' => 'حجم الملف كبير جداً'];
    }
    
    $upload_dir = UPLOAD_PATH . date('Y/m/');
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    $filename = uniqid() . '.' . $file_extension;
    $filepath = $upload_dir . $filename;
    
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        return ['success' => true, 'filename' => $filename, 'filepath' => $filepath];
    }
    
    return ['success' => false, 'message' => 'فشل في رفع الملف'];
}

// دالة لحساب المدة المتبقية
function calculateRemainingTime($end_date) {
    $now = new DateTime();
    $end = new DateTime($end_date);
    
    if ($end < $now) {
        return 'منتهي الصلاحية';
    }
    
    $diff = $now->diff($end);
    
    if ($diff->days > 30) {
        $months = floor($diff->days / 30);
        return $months . ' شهر';
    } elseif ($diff->days > 0) {
        return $diff->days . ' يوم';
    } else {
        return $diff->h . ' ساعة';
    }
}

// دالة لتنظيف وتحسين النصوص
function cleanText($text) {
    $text = trim($text);
    $text = strip_tags($text);
    $text = htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
    return $text;
}

// دالة لتقصير النص
function truncateText($text, $length = 100, $suffix = '...') {
    if (mb_strlen($text, 'UTF-8') <= $length) {
        return $text;
    }
    
    return mb_substr($text, 0, $length, 'UTF-8') . $suffix;
}

// دالة لتوليد كلمة مرور قوية
function generateStrongPassword($length = 12) {
    $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    return substr(str_shuffle($chars), 0, $length);
}

// دالة للتحقق من قوة كلمة المرور
function isStrongPassword($password) {
    return strlen($password) >= 8 && 
           preg_match('/[A-Z]/', $password) && 
           preg_match('/[a-z]/', $password) && 
           preg_match('/[0-9]/', $password);
}
?>

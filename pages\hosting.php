<?php
// جلب خطط الاستضافة من قاعدة البيانات
$hosting_plans = fetchAll("SELECT * FROM services WHERE category_id = 1 AND status = 'active' ORDER BY price ASC");
?>

<!-- صفحة خطط الاستضافة -->
<div class="hosting-page">
    <!-- قسم العنوان الرئيسي -->
    <section class="page-header bg-primary text-white py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold mb-3">خدمات الاستضافة المشتركة</h1>
                    <p class="lead mb-4">
                        اختر الخطة المناسبة لموقعك من بين مجموعة متنوعة من خطط الاستضافة المشتركة 
                        المصممة لتلبية احتياجات المواقع الشخصية والتجارية
                    </p>
                    <div class="features-list">
                        <span class="badge bg-success me-2 mb-2">
                            <i class="fas fa-check me-1"></i>ضمان وقت التشغيل 99.9%
                        </span>
                        <span class="badge bg-success me-2 mb-2">
                            <i class="fas fa-check me-1"></i>دعم فني 24/7
                        </span>
                        <span class="badge bg-success me-2 mb-2">
                            <i class="fas fa-check me-1"></i>شهادات SSL مجانية
                        </span>
                        <span class="badge bg-success me-2 mb-2">
                            <i class="fas fa-check me-1"></i>نسخ احتياطية يومية
                        </span>
                    </div>
                </div>
                <div class="col-lg-4 text-center">
                    <img src="assets/images/hosting-illustration.svg" alt="خدمات الاستضافة" class="img-fluid" style="max-height: 300px;">
                </div>
            </div>
        </div>
    </section>

    <!-- قسم خطط الاستضافة -->
    <section class="pricing-section section-padding">
        <div class="container">
            <div class="section-title text-center mb-5">
                <h2>اختر الخطة المناسبة لك</h2>
                <p>جميع الخطط تشمل مميزات أساسية مع إمكانية الترقية في أي وقت</p>
            </div>

            <!-- مفتاح التبديل بين الفترات -->
            <div class="billing-toggle text-center mb-5">
                <div class="btn-group" role="group">
                    <input type="radio" class="btn-check" name="billing" id="monthly" checked>
                    <label class="btn btn-outline-primary" for="monthly">شهري</label>
                    
                    <input type="radio" class="btn-check" name="billing" id="annually">
                    <label class="btn btn-outline-primary" for="annually">
                        سنوي <span class="badge bg-success">وفر 20%</span>
                    </label>
                </div>
            </div>

            <div class="row justify-content-center">
                <?php if ($hosting_plans): ?>
                    <?php foreach ($hosting_plans as $index => $plan): ?>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="pricing-card <?php echo $index == 1 ? 'featured' : ''; ?> h-100">
                                <?php if ($index == 1): ?>
                                    <div class="popular-badge">الأكثر شعبية</div>
                                <?php endif; ?>
                                
                                <div class="card-header text-center">
                                    <h4 class="plan-name"><?php echo htmlspecialchars($plan['name']); ?></h4>
                                    <div class="price-display">
                                        <span class="price monthly-price" data-monthly="<?php echo $plan['price']; ?>">
                                            <?php echo number_format($plan['price'], 0); ?>
                                        </span>
                                        <span class="price annually-price" data-annually="<?php echo $plan['price'] * 12 * 0.8; ?>" style="display: none;">
                                            <?php echo number_format($plan['price'] * 12 * 0.8, 0); ?>
                                        </span>
                                        <span class="currency">جنيه</span>
                                        <span class="period monthly-period">/شهر</span>
                                        <span class="period annually-period" style="display: none;">/سنة</span>
                                    </div>
                                    <?php if ($plan['setup_fee'] > 0): ?>
                                        <small class="setup-fee text-muted">
                                            رسوم الإعداد: <?php echo number_format($plan['setup_fee'], 0); ?> جنيه
                                        </small>
                                    <?php endif; ?>
                                </div>

                                <div class="card-body">
                                    <p class="plan-description text-muted mb-4">
                                        <?php echo htmlspecialchars($plan['description']); ?>
                                    </p>

                                    <ul class="features-list list-unstyled">
                                        <li class="mb-2">
                                            <i class="fas fa-hdd text-primary me-2"></i>
                                            <strong>مساحة التخزين:</strong> 
                                            <?php echo $plan['disk_space'] ? $plan['disk_space'] . ' جيجا' : 'غير محدود'; ?>
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-exchange-alt text-primary me-2"></i>
                                            <strong>نقل البيانات:</strong> 
                                            <?php echo $plan['bandwidth'] ? $plan['bandwidth'] . ' جيجا' : 'غير محدود'; ?>
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-globe text-primary me-2"></i>
                                            <strong>المواقع:</strong> 
                                            <?php echo $plan['domains_limit'] == 0 ? 'غير محدود' : $plan['domains_limit']; ?>
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-envelope text-primary me-2"></i>
                                            <strong>حسابات البريد:</strong> 
                                            <?php echo $plan['email_accounts'] == 0 ? 'غير محدود' : $plan['email_accounts']; ?>
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-database text-primary me-2"></i>
                                            <strong>قواعد البيانات:</strong> 
                                            <?php echo $plan['databases'] == 0 ? 'غير محدود' : $plan['databases']; ?>
                                        </li>
                                        <?php if ($plan['ssl_included']): ?>
                                            <li class="mb-2">
                                                <i class="fas fa-shield-alt text-success me-2"></i>
                                                <strong>شهادة SSL مجانية</strong>
                                            </li>
                                        <?php endif; ?>
                                        <?php if ($plan['backup_included']): ?>
                                            <li class="mb-2">
                                                <i class="fas fa-save text-success me-2"></i>
                                                <strong>نسخ احتياطية يومية</strong>
                                            </li>
                                        <?php endif; ?>
                                        <li class="mb-2">
                                            <i class="fas fa-headset text-primary me-2"></i>
                                            <strong>دعم فني:</strong> 
                                            <?php 
                                            $support_levels = [
                                                'basic' => 'أساسي',
                                                'priority' => 'أولوية',
                                                'premium' => 'مميز'
                                            ];
                                            echo $support_levels[$plan['support_level']];
                                            ?>
                                        </li>
                                    </ul>
                                </div>

                                <div class="card-footer text-center">
                                    <a href="?page=order&service_id=<?php echo $plan['id']; ?>" 
                                       class="btn <?php echo $index == 1 ? 'btn-primary' : 'btn-outline-primary'; ?> btn-lg w-100">
                                        <i class="fas fa-shopping-cart me-2"></i>
                                        اطلب الآن
                                    </a>
                                    <small class="text-muted d-block mt-2">
                                        إعداد فوري • ضمان استرداد المال لمدة 30 يوم
                                    </small>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="col-12 text-center">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            لا توجد خطط استضافة متاحة حالياً. يرجى المحاولة لاحقاً.
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- مقارنة الخطط -->
            <div class="comparison-section mt-5">
                <div class="text-center mb-4">
                    <button class="btn btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#planComparison">
                        <i class="fas fa-balance-scale me-2"></i>
                        مقارنة تفصيلية للخطط
                    </button>
                </div>
                
                <div class="collapse" id="planComparison">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead class="table-dark">
                                <tr>
                                    <th>المميزة</th>
                                    <?php foreach ($hosting_plans as $plan): ?>
                                        <th class="text-center"><?php echo htmlspecialchars($plan['name']); ?></th>
                                    <?php endforeach; ?>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>السعر الشهري</strong></td>
                                    <?php foreach ($hosting_plans as $plan): ?>
                                        <td class="text-center"><?php echo number_format($plan['price'], 0); ?> جنيه</td>
                                    <?php endforeach; ?>
                                </tr>
                                <tr>
                                    <td><strong>مساحة التخزين</strong></td>
                                    <?php foreach ($hosting_plans as $plan): ?>
                                        <td class="text-center">
                                            <?php echo $plan['disk_space'] ? $plan['disk_space'] . ' جيجا' : 'غير محدود'; ?>
                                        </td>
                                    <?php endforeach; ?>
                                </tr>
                                <tr>
                                    <td><strong>نقل البيانات</strong></td>
                                    <?php foreach ($hosting_plans as $plan): ?>
                                        <td class="text-center">
                                            <?php echo $plan['bandwidth'] ? $plan['bandwidth'] . ' جيجا' : 'غير محدود'; ?>
                                        </td>
                                    <?php endforeach; ?>
                                </tr>
                                <tr>
                                    <td><strong>عدد المواقع</strong></td>
                                    <?php foreach ($hosting_plans as $plan): ?>
                                        <td class="text-center">
                                            <?php echo $plan['domains_limit'] == 0 ? 'غير محدود' : $plan['domains_limit']; ?>
                                        </td>
                                    <?php endforeach; ?>
                                </tr>
                                <tr>
                                    <td><strong>حسابات البريد</strong></td>
                                    <?php foreach ($hosting_plans as $plan): ?>
                                        <td class="text-center">
                                            <?php echo $plan['email_accounts'] == 0 ? 'غير محدود' : $plan['email_accounts']; ?>
                                        </td>
                                    <?php endforeach; ?>
                                </tr>
                                <tr>
                                    <td><strong>شهادة SSL</strong></td>
                                    <?php foreach ($hosting_plans as $plan): ?>
                                        <td class="text-center">
                                            <?php echo $plan['ssl_included'] ? '<i class="fas fa-check text-success"></i>' : '<i class="fas fa-times text-danger"></i>'; ?>
                                        </td>
                                    <?php endforeach; ?>
                                </tr>
                                <tr>
                                    <td><strong>النسخ الاحتياطية</strong></td>
                                    <?php foreach ($hosting_plans as $plan): ?>
                                        <td class="text-center">
                                            <?php echo $plan['backup_included'] ? '<i class="fas fa-check text-success"></i>' : '<i class="fas fa-times text-danger"></i>'; ?>
                                        </td>
                                    <?php endforeach; ?>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- قسم المميزات الإضافية -->
    <section class="features-section bg-light section-padding">
        <div class="container">
            <div class="section-title text-center mb-5">
                <h2>مميزات إضافية مع جميع الخطط</h2>
                <p>نحن نقدم أكثر من مجرد استضافة</p>
            </div>
            
            <div class="row">
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="feature-card text-center h-100">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-tachometer-alt fa-3x text-primary"></i>
                        </div>
                        <h5>أداء فائق</h5>
                        <p>سيرفرات SSD عالية السرعة مع تقنيات التخزين المؤقت المتقدمة</p>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="feature-card text-center h-100">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-shield-alt fa-3x text-success"></i>
                        </div>
                        <h5>حماية متقدمة</h5>
                        <p>جدار حماية متقدم ومراقبة أمنية مستمرة ضد التهديدات</p>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="feature-card text-center h-100">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-tools fa-3x text-warning"></i>
                        </div>
                        <h5>أدوات متقدمة</h5>
                        <p>لوحة تحكم cPanel مع أدوات إدارة المواقع والقواعد</p>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="feature-card text-center h-100">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-headset fa-3x text-info"></i>
                        </div>
                        <h5>دعم متخصص</h5>
                        <p>فريق دعم فني عربي متخصص متاح 24/7 عبر عدة قنوات</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<style>
.pricing-card {
    position: relative;
    border: 2px solid #e9ecef;
    border-radius: 15px;
    transition: all 0.3s ease;
    overflow: hidden;
}

.pricing-card.featured {
    border-color: var(--primary-color);
    transform: scale(1.05);
    box-shadow: 0 10px 30px rgba(44, 90, 160, 0.2);
}

.popular-badge {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    padding: 5px 20px;
    border-radius: 15px;
    font-size: 0.85rem;
    font-weight: 600;
    z-index: 1;
}

.price-display {
    margin: 20px 0;
}

.price {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-color);
}

.currency, .period {
    font-size: 1.2rem;
    color: var(--text-light);
}

.features-list li {
    padding: 8px 0;
    border-bottom: 1px solid #f8f9fa;
}

.features-list li:last-child {
    border-bottom: none;
}

.feature-card {
    background: white;
    padding: 30px 20px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
}

.billing-toggle .btn-check:checked + .btn {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}
</style>

<script>
// تبديل عرض الأسعار بين الشهري والسنوي
document.addEventListener('DOMContentLoaded', function() {
    const monthlyRadio = document.getElementById('monthly');
    const annuallyRadio = document.getElementById('annually');
    
    function updatePricing() {
        const isAnnual = annuallyRadio.checked;
        
        document.querySelectorAll('.monthly-price, .monthly-period').forEach(el => {
            el.style.display = isAnnual ? 'none' : 'inline';
        });
        
        document.querySelectorAll('.annually-price, .annually-period').forEach(el => {
            el.style.display = isAnnual ? 'inline' : 'none';
        });
    }
    
    monthlyRadio.addEventListener('change', updatePricing);
    annuallyRadio.addEventListener('change', updatePricing);
});
</script>

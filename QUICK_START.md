# دليل البدء السريع - نقرة هوست

## خطوات التثبيت السريع

### 1. إعداد قاعدة البيانات
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE nakra_host CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- استيراد الجداول
SOURCE database/schema.sql;
```

### 2. تحديث إعدادات الاتصال
```php
// في ملف config/database.php
define('DB_HOST', 'localhost');
define('DB_NAME', 'nakra_host');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

### 3. تحديث إعدادات الموقع
```php
// في ملف config/config.php
define('SITE_URL', 'https://nakra.host');
define('SITE_EMAIL', '<EMAIL>');
define('ENCRYPTION_KEY', 'your-unique-32-character-key-here');
```

### 4. إعداد الصلاحيات
```bash
chmod 755 uploads/ logs/ backups/
chmod 644 .htaccess
```

## الحسابات الافتراضية

### مدير النظام
- **البريد:** <EMAIL>
- **كلمة المرور:** admin123
- **رابط الدخول:** https://nakra.host/admin/

⚠️ **مهم:** غير كلمة المرور فوراً!

## الملفات المطلوبة

### ملفات الصور (يجب إضافتها)
```
assets/images/
├── logo.png (شعار الموقع)
├── logo-white.png (شعار أبيض)
├── favicon.ico (أيقونة الموقع)
├── hero-server.png (صورة البانر)
├── hosting-illustration.svg (رسم توضيحي)
└── og-image.jpg (صورة المشاركة)
```

### ملفات CSS الإضافية
```
assets/css/
├── bootstrap.rtl.min.css (Bootstrap RTL)
└── responsive.css (تجاوب إضافي)
```

### ملفات JavaScript
```
assets/js/
├── bootstrap.bundle.min.js
├── jquery-3.6.0.min.js
└── main.js (موجود)
```

## اختبار سريع

### 1. اختبار الصفحة الرئيسية
- افتح: `https://nakra.host`
- تأكد من ظهور الموقع بشكل صحيح

### 2. اختبار تسجيل الدخول
- اذهب إلى: `https://nakra.host/admin/`
- استخدم الحساب الافتراضي

### 3. اختبار قاعدة البيانات
- تأكد من ظهور البيانات في لوحة التحكم
- اختبر إضافة عميل جديد

## إعدادات الإنتاج

### 1. الأمان
```php
// في config/config.php
define('DEBUG_MODE', false);
define('MAINTENANCE_MODE', false);
```

### 2. HTTPS
```apache
# في .htaccess (إلغاء التعليق)
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
```

### 3. البريد الإلكتروني
```php
// إعداد SMTP في config/config.php
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
```

## المشاكل الشائعة

### خطأ في الاتصال بقاعدة البيانات
- تأكد من صحة بيانات الاتصال
- تأكد من تشغيل MySQL
- تأكد من الصلاحيات

### الصفحات لا تظهر بشكل صحيح
- تأكد من تفعيل mod_rewrite
- تأكد من صلاحيات ملف .htaccess
- تأكد من مسار الملفات

### مشاكل الترميز العربي
- تأكد من ترميز UTF-8 في قاعدة البيانات
- تأكد من إعدادات PHP للترميز
- تأكد من ترميز الملفات

## الدعم السريع

### معلومات الاتصال
- **الهاتف:** 01062751630
- **البريد:** <EMAIL>
- **الموقع:** https://www.nakraformarketing.com

### الملفات المهمة
- `config/database.php` - إعدادات قاعدة البيانات
- `config/config.php` - إعدادات الموقع العامة
- `.htaccess` - إعدادات الخادم
- `database/schema.sql` - هيكل قاعدة البيانات

---

🎉 **مبروك!** موقع نقرة هوست جاهز للعمل!

// ===== متغيرات عامة =====
let isRTL = document.documentElement.dir === 'rtl';

// ===== تهيئة الموقع عند التحميل =====
document.addEventListener('DOMContentLoaded', function() {
    initializeWebsite();
    initializeAnimations();
    initializeForms();
    initializeTooltips();
    initializeModals();
    initializeLiveChat();
});

// ===== تهيئة الموقع =====
function initializeWebsite() {
    // تحسين الأداء - تحميل الصور بشكل تدريجي
    lazyLoadImages();
    
    // تحسين تجربة المستخدم
    smoothScrolling();
    
    // تحديث الوقت الحالي
    updateCurrentTime();
    setInterval(updateCurrentTime, 60000); // كل دقيقة
    
    // مراقبة حالة الاتصال
    monitorConnection();
}

// ===== تحميل الصور التدريجي =====
function lazyLoadImages() {
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });

    images.forEach(img => imageObserver.observe(img));
}

// ===== التمرير السلس =====
function smoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// ===== الرسوم المتحركة =====
function initializeAnimations() {
    // مراقب التقاطع للرسوم المتحركة
    const animationObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate');
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });

    // مراقبة العناصر المتحركة
    document.querySelectorAll('.fade-in-up, .slide-in-left, .slide-in-right, .zoom-in').forEach(el => {
        animationObserver.observe(el);
    });

    // تأثير الكتابة
    typeWriterEffect();
}

// ===== تأثير الكتابة =====
function typeWriterEffect() {
    const elements = document.querySelectorAll('.typewriter');
    elements.forEach(element => {
        const text = element.textContent;
        element.textContent = '';
        element.style.borderRight = '2px solid';
        
        let i = 0;
        const timer = setInterval(() => {
            element.textContent += text.charAt(i);
            i++;
            if (i > text.length) {
                clearInterval(timer);
                element.style.borderRight = 'none';
            }
        }, 100);
    });
}

// ===== تهيئة النماذج =====
function initializeForms() {
    // التحقق من صحة النماذج
    const forms = document.querySelectorAll('.needs-validation');
    forms.forEach(form => {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });

    // تحسين حقول الإدخال
    enhanceInputFields();
    
    // معالج البحث المباشر
    initializeLiveSearch();
}

// ===== تحسين حقول الإدخال =====
function enhanceInputFields() {
    // إضافة تأثيرات للحقول
    document.querySelectorAll('.form-control').forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            if (!this.value) {
                this.parentElement.classList.remove('focused');
            }
        });
        
        // التحقق من القوة لكلمات المرور
        if (input.type === 'password') {
            input.addEventListener('input', checkPasswordStrength);
        }
    });
}

// ===== فحص قوة كلمة المرور =====
function checkPasswordStrength(event) {
    const password = event.target.value;
    const strengthMeter = document.getElementById('password-strength');
    
    if (!strengthMeter) return;
    
    let strength = 0;
    let feedback = '';
    
    if (password.length >= 8) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    
    switch (strength) {
        case 0:
        case 1:
            feedback = 'ضعيفة جداً';
            strengthMeter.className = 'password-strength weak';
            break;
        case 2:
            feedback = 'ضعيفة';
            strengthMeter.className = 'password-strength fair';
            break;
        case 3:
            feedback = 'متوسطة';
            strengthMeter.className = 'password-strength good';
            break;
        case 4:
        case 5:
            feedback = 'قوية';
            strengthMeter.className = 'password-strength strong';
            break;
    }
    
    strengthMeter.textContent = feedback;
}

// ===== البحث المباشر =====
function initializeLiveSearch() {
    const searchInput = document.getElementById('live-search');
    if (!searchInput) return;
    
    let searchTimeout;
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            performLiveSearch(this.value);
        }, 300);
    });
}

function performLiveSearch(query) {
    if (query.length < 2) return;
    
    fetch(`api/search.php?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            displaySearchResults(data);
        })
        .catch(error => {
            console.error('خطأ في البحث:', error);
        });
}

function displaySearchResults(results) {
    const resultsContainer = document.getElementById('search-results');
    if (!resultsContainer) return;
    
    resultsContainer.innerHTML = '';
    
    if (results.length === 0) {
        resultsContainer.innerHTML = '<div class="no-results">لا توجد نتائج</div>';
        return;
    }
    
    results.forEach(result => {
        const resultElement = document.createElement('div');
        resultElement.className = 'search-result-item';
        resultElement.innerHTML = `
            <h6>${result.title}</h6>
            <p>${result.description}</p>
            <a href="${result.url}" class="btn btn-sm btn-outline-primary">عرض</a>
        `;
        resultsContainer.appendChild(resultElement);
    });
}

// ===== تهيئة التلميحات =====
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// ===== تهيئة النوافذ المنبثقة =====
function initializeModals() {
    // إغلاق النوافذ بالضغط على Escape
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            const openModals = document.querySelectorAll('.modal.show');
            openModals.forEach(modal => {
                const modalInstance = bootstrap.Modal.getInstance(modal);
                if (modalInstance) modalInstance.hide();
            });
        }
    });
}

// ===== الدردشة المباشرة =====
function initializeLiveChat() {
    const chatWidget = document.getElementById('live-chat');
    if (!chatWidget) return;
    
    // إنشاء نافذة الدردشة
    createChatWindow();
}

function createChatWindow() {
    const chatWindow = document.createElement('div');
    chatWindow.id = 'chat-window';
    chatWindow.className = 'chat-window';
    chatWindow.style.display = 'none';
    chatWindow.innerHTML = `
        <div class="chat-header">
            <h6>دردشة مباشرة</h6>
            <button class="btn-close" onclick="toggleChat()"></button>
        </div>
        <div class="chat-messages" id="chat-messages">
            <div class="message bot-message">
                مرحباً! كيف يمكنني مساعدتك اليوم؟
            </div>
        </div>
        <div class="chat-input">
            <input type="text" id="chat-input" placeholder="اكتب رسالتك..." class="form-control">
            <button onclick="sendMessage()" class="btn btn-primary">إرسال</button>
        </div>
    `;
    
    document.body.appendChild(chatWindow);
    
    // إضافة مستمع للضغط على Enter
    document.getElementById('chat-input').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            sendMessage();
        }
    });
}

function toggleChat() {
    const chatWindow = document.getElementById('chat-window');
    const chatButton = document.querySelector('.chat-button');
    
    if (chatWindow.style.display === 'none') {
        chatWindow.style.display = 'block';
        chatButton.style.display = 'none';
    } else {
        chatWindow.style.display = 'none';
        chatButton.style.display = 'flex';
    }
}

function sendMessage() {
    const input = document.getElementById('chat-input');
    const message = input.value.trim();
    
    if (!message) return;
    
    // إضافة رسالة المستخدم
    addMessageToChat(message, 'user');
    input.value = '';
    
    // محاكاة رد تلقائي
    setTimeout(() => {
        const botResponse = getBotResponse(message);
        addMessageToChat(botResponse, 'bot');
    }, 1000);
}

function addMessageToChat(message, sender) {
    const messagesContainer = document.getElementById('chat-messages');
    const messageElement = document.createElement('div');
    messageElement.className = `message ${sender}-message`;
    messageElement.textContent = message;
    
    messagesContainer.appendChild(messageElement);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

function getBotResponse(userMessage) {
    const responses = {
        'مرحبا': 'مرحباً بك! كيف يمكنني مساعدتك؟',
        'الأسعار': 'يمكنك الاطلاع على أسعارنا في صفحة الخدمات',
        'الدعم': 'فريق الدعم الفني متاح 24/7 لمساعدتك',
        'default': 'شكراً لتواصلك معنا. سيقوم أحد ممثلي خدمة العملاء بالرد عليك قريباً.'
    };
    
    const lowerMessage = userMessage.toLowerCase();
    
    for (const [key, response] of Object.entries(responses)) {
        if (lowerMessage.includes(key)) {
            return response;
        }
    }
    
    return responses.default;
}

// ===== تحديث الوقت =====
function updateCurrentTime() {
    const timeElements = document.querySelectorAll('.current-time');
    const now = new Date();
    const timeString = now.toLocaleString('ar-EG', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
    
    timeElements.forEach(element => {
        element.textContent = timeString;
    });
}

// ===== مراقبة حالة الاتصال =====
function monitorConnection() {
    window.addEventListener('online', function() {
        showNotification('تم استعادة الاتصال بالإنترنت', 'success');
    });
    
    window.addEventListener('offline', function() {
        showNotification('انقطع الاتصال بالإنترنت', 'warning');
    });
}

// ===== عرض الإشعارات =====
function showNotification(message, type = 'info', duration = 5000) {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // إزالة الإشعار تلقائياً
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, duration);
}

// ===== دوال مساعدة =====

// تنسيق الأرقام
function formatNumber(number) {
    return new Intl.NumberFormat('ar-EG').format(number);
}

// تنسيق العملة
function formatCurrency(amount, currency = 'EGP') {
    return new Intl.NumberFormat('ar-EG', {
        style: 'currency',
        currency: currency
    }).format(amount);
}

// نسخ النص إلى الحافظة
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showNotification('تم نسخ النص بنجاح', 'success');
    }).catch(() => {
        showNotification('فشل في نسخ النص', 'error');
    });
}

// تحميل المحتوى بشكل ديناميكي
function loadContent(url, containerId) {
    const container = document.getElementById(containerId);
    if (!container) return;
    
    container.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> جاري التحميل...</div>';
    
    fetch(url)
        .then(response => response.text())
        .then(html => {
            container.innerHTML = html;
        })
        .catch(error => {
            container.innerHTML = '<div class="alert alert-danger">حدث خطأ في تحميل المحتوى</div>';
            console.error('خطأ في تحميل المحتوى:', error);
        });
}

// التحقق من صحة البريد الإلكتروني
function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
}

// التحقق من صحة رقم الهاتف
function validatePhone(phone) {
    const re = /^[\+]?[1-9][\d]{0,15}$/;
    return re.test(phone.replace(/\s/g, ''));
}

// ===== تصدير الدوال للاستخدام العام =====
window.showNotification = showNotification;
window.toggleChat = toggleChat;
window.sendMessage = sendMessage;
window.copyToClipboard = copyToClipboard;
window.loadContent = loadContent;
window.formatCurrency = formatCurrency;
window.formatNumber = formatNumber;

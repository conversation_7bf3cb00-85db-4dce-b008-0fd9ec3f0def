# ===== إعدادات الأمان =====

# منع الوصول للملفات الحساسة
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

<Files ~ "\.(sql|log|txt|md)$">
    Order allow,deny
    Deny from all
</Files>

# حماية ملفات التكوين
<Files "config.php">
    Order allow,deny
    Deny from all
</Files>

<Files "database.php">
    Order allow,deny
    Deny from all
</Files>

# منع عرض محتويات المجلدات
Options -Indexes

# حماية من هجمات XSS
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
</IfModule>

# ===== إعدادات الضغط =====
<IfModule mod_deflate.c>
    # ضغط النصوص والملفات
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# ===== إعدادات التخزين المؤقت =====
<IfModule mod_expires.c>
    ExpiresActive On
    
    # الصور
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    
    # الخطوط
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
    
    # CSS و JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    
    # HTML
    ExpiresByType text/html "access plus 1 hour"
    
    # ملفات أخرى
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/x-javascript "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
</IfModule>

# ===== إعدادات Cache-Control =====
<IfModule mod_headers.c>
    # الصور والخطوط
    <FilesMatch "\.(ico|jpe?g|png|gif|webp|svg|woff|woff2|ttf|eot)$">
        Header set Cache-Control "public, max-age=2592000"
    </FilesMatch>
    
    # CSS و JavaScript
    <FilesMatch "\.(css|js)$">
        Header set Cache-Control "public, max-age=604800"
    </FilesMatch>
    
    # HTML
    <FilesMatch "\.(html|htm|php)$">
        Header set Cache-Control "public, max-age=3600"
    </FilesMatch>
</IfModule>

# ===== إعدادات إعادة الكتابة =====
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # إجبار HTTPS (إلغاء التعليق عند الحاجة)
    # RewriteCond %{HTTPS} off
    # RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
    
    # إزالة www (إلغاء التعليق عند الحاجة)
    # RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
    # RewriteRule ^(.*)$ https://%1/$1 [R=301,L]
    
    # روابط صديقة لمحركات البحث
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^([^/]+)/?$ index.php?page=$1 [L,QSA]
    
    # إعادة توجيه الصفحات القديمة
    RewriteRule ^hosting-plans/?$ index.php?page=hosting [R=301,L]
    RewriteRule ^vps-servers/?$ index.php?page=vps [R=301,L]
    RewriteRule ^dedicated-servers/?$ index.php?page=dedicated [R=301,L]
    
    # منع الوصول للمجلدات الحساسة
    RewriteRule ^(config|includes|database|logs|backups)/ - [F,L]
</IfModule>

# ===== إعدادات PHP =====
<IfModule mod_php7.c>
    # إخفاء معلومات PHP
    php_flag expose_php off
    
    # تحسين الأداء
    php_value memory_limit 256M
    php_value max_execution_time 300
    php_value max_input_time 300
    php_value post_max_size 64M
    php_value upload_max_filesize 64M
    
    # إعدادات الجلسة
    php_value session.cookie_httponly 1
    php_value session.cookie_secure 1
    php_value session.use_strict_mode 1
    
    # إعدادات الأمان
    php_flag register_globals off
    php_flag magic_quotes_gpc off
    php_flag allow_url_fopen off
    php_flag allow_url_include off
</IfModule>

# ===== حماية من الهجمات =====

# منع حقن SQL
<IfModule mod_rewrite.c>
    RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*iframe.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*object.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*embed.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (SELECT|UNION|INSERT|DROP|DELETE|UPDATE|CREATE|ALTER|EXEC) [NC]
    RewriteRule ^(.*)$ - [F,L]
</IfModule>

# منع User Agents المشبوهة
<IfModule mod_rewrite.c>
    RewriteCond %{HTTP_USER_AGENT} ^$ [OR]
    RewriteCond %{HTTP_USER_AGENT} ^(java|curl|wget) [NC,OR]
    RewriteCond %{HTTP_USER_AGENT} (winhttp|HTTrack|clshttp|archiver|loader|email|harvest|extract|grab|miner) [NC]
    RewriteRule ^(.*)$ - [F,L]
</IfModule>

# ===== صفحات الأخطاء المخصصة =====
ErrorDocument 400 /error.php?code=400
ErrorDocument 401 /error.php?code=401
ErrorDocument 403 /error.php?code=403
ErrorDocument 404 /error.php?code=404
ErrorDocument 500 /error.php?code=500

# ===== إعدادات MIME =====
<IfModule mod_mime.c>
    # الخطوط
    AddType application/font-woff woff
    AddType application/font-woff2 woff2
    AddType application/vnd.ms-fontobject eot
    AddType application/x-font-ttf ttf
    AddType image/svg+xml svg
    
    # ملفات أخرى
    AddType application/json json
    AddType application/ld+json jsonld
    AddType text/cache-manifest appcache
</IfModule>

# ===== تحسينات إضافية =====

# تقليل استهلاك الذاكرة
<IfModule mod_env.c>
    SetEnv no-gzip dont-vary
</IfModule>

# منع hotlinking للصور
<IfModule mod_rewrite.c>
    RewriteCond %{HTTP_REFERER} !^$
    RewriteCond %{HTTP_REFERER} !^https?://(www\.)?nakra\.host [NC]
    RewriteCond %{REQUEST_URI} \.(jpe?g|png|gif|webp)$ [NC]
    RewriteRule \.(jpe?g|png|gif|webp)$ /assets/images/hotlink-protection.png [R=302,L]
</IfModule>

# ===== إعدادات الترميز =====
AddDefaultCharset UTF-8
<IfModule mod_mime.c>
    AddCharset UTF-8 .html .css .js .xml .json .rss .atom
</IfModule>

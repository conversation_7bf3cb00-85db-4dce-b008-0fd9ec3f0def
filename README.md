# نقرة هوست - نظام إدارة خدمات الاستضافة

نظام إدارة شامل لخدمات الاستضافة والسيرفرات مصمم خصيصاً للشركات العربية. يوفر واجهة احترافية لإدارة العملاء والطلبات والفواتير مع دعم كامل للغة العربية.

## المميزات الرئيسية

### 🎨 التصميم والواجهة
- تصميم احترافي متجاوب مع جميع الأجهزة
- ألوان هادئة ومتناسقة
- خط عربي احترافي (Cairo Font)
- واجهة مستخدم سهلة وبديهية
- دعم كامل للغة العربية (RTL)

### 🛡️ الأمان والحماية
- تشفير كلمات المرور باستخدام bcrypt
- حماية من هجمات CSRF
- تسجيل جميع الأنشطة
- جلسات آمنة مع انتهاء صلاحية
- حماية من حقن SQL
- رؤوس أمان HTTP

### 📊 لوحة التحكم الإدارية
- لوحة تحكم شاملة مثل WHMCS
- إحصائيات مفصلة ورسوم بيانية
- إدارة العملاء والطلبات
- نظام فواتير متقدم
- إدارة الدعم الفني
- تقارير مالية وإحصائية

### 💳 نظام الفواتير والمدفوعات
- إنشاء فواتير تلقائية
- دعم طرق دفع متعددة
- تتبع المدفوعات والمستحقات
- تذكيرات الدفع التلقائية
- تقارير مالية مفصلة

### 🎫 نظام الدعم الفني
- نظام تذاكر متقدم
- أولويات مختلفة للتذاكر
- تتبع حالة التذاكر
- ردود تلقائية
- إحصائيات الدعم

### 🌐 إدارة الخدمات
- خطط استضافة متنوعة
- سيرفرات افتراضية ومخصصة
- إدارة النطاقات
- خدمات إضافية (SSL، النسخ الاحتياطي)
- تفعيل وإيقاف الخدمات

## متطلبات النظام

### الخادم
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث / MariaDB 10.2 أو أحدث
- Apache 2.4 أو Nginx
- mod_rewrite مفعل
- OpenSSL
- cURL
- GD Library

### قاعدة البيانات
- MySQL/MariaDB مع دعم UTF-8
- صلاحيات إنشاء وتعديل الجداول
- مساحة تخزين كافية (500MB على الأقل)

## تعليمات التثبيت

### 1. تحضير الملفات
```bash
# تحميل الملفات إلى مجلد الموقع
cd /path/to/your/website
# رفع جميع الملفات
```

### 2. إعداد قاعدة البيانات
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE nakra_host CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- إنشاء مستخدم قاعدة البيانات
CREATE USER 'nakra_user'@'localhost' IDENTIFIED BY 'strong_password_here';
GRANT ALL PRIVILEGES ON nakra_host.* TO 'nakra_user'@'localhost';
FLUSH PRIVILEGES;

-- استيراد الجداول
SOURCE database/schema.sql;
```

### 3. تكوين الإعدادات
```php
// تحرير ملف config/database.php
define('DB_HOST', 'localhost');
define('DB_NAME', 'nakra_host');
define('DB_USER', 'nakra_user');
define('DB_PASS', 'your_password_here');

// تحرير ملف config/config.php
define('SITE_URL', 'https://nakra.host');
define('SITE_EMAIL', '<EMAIL>');
define('SITE_PHONE', '01062751630');
define('ENCRYPTION_KEY', 'your-unique-encryption-key-here');
```

### 4. إعداد الصلاحيات
```bash
# إعطاء صلاحيات الكتابة للمجلدات المطلوبة
chmod 755 uploads/
chmod 755 logs/
chmod 755 backups/
chmod 644 .htaccess
```

### 5. إعداد Cron Jobs (اختياري)
```bash
# إضافة المهام التلقائية
# تشغيل كل ساعة لإرسال التذكيرات
0 * * * * /usr/bin/php /path/to/website/cron/send_reminders.php

# تشغيل يومياً للنسخ الاحتياطي
0 2 * * * /usr/bin/php /path/to/website/cron/backup.php

# تشغيل أسبوعياً لتنظيف الملفات المؤقتة
0 3 * * 0 /usr/bin/php /path/to/website/cron/cleanup.php
```

## الحسابات الافتراضية

### حساب المدير
- **البريد الإلكتروني:** <EMAIL>
- **كلمة المرور:** admin123
- **الدور:** مدير النظام

> ⚠️ **مهم:** يجب تغيير كلمة المرور فوراً بعد التثبيت!

## إعداد البريد الإلكتروني

### SMTP Configuration
```php
// في ملف config/config.php
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
define('SMTP_ENCRYPTION', 'tls');
```

### إعداد Gmail
1. تفعيل التحقق بخطوتين
2. إنشاء كلمة مرور للتطبيق
3. استخدام كلمة مرور التطبيق في الإعدادات

## إعداد طرق الدفع

### PayPal
```php
define('PAYPAL_CLIENT_ID', 'your-paypal-client-id');
define('PAYPAL_CLIENT_SECRET', 'your-paypal-client-secret');
define('PAYPAL_MODE', 'sandbox'); // أو 'live' للإنتاج
```

### طرق الدفع المحلية
- فودافون كاش
- أورانج موني
- التحويل البنكي
- البطاقات الائتمانية

## الأمان والحماية

### إعدادات الأمان المطلوبة
1. **تغيير كلمات المرور الافتراضية**
2. **تفعيل HTTPS**
3. **إعداد جدار الحماية**
4. **تحديث PHP وMySQL بانتظام**
5. **مراقبة ملفات السجل**

### النسخ الاحتياطية
- نسخ احتياطية يومية لقاعدة البيانات
- نسخ احتياطية أسبوعية للملفات
- تخزين النسخ في مكان آمن منفصل

## الدعم والمساعدة

### الوثائق
- دليل المستخدم: `/docs/user-guide.pdf`
- دليل المطور: `/docs/developer-guide.pdf`
- API Documentation: `/docs/api.md`

### الدعم الفني
- **البريد الإلكتروني:** <EMAIL>
- **الهاتف:** 01062751630
- **الموقع:** https://www.nakraformarketing.com

### المجتمع
- منتدى الدعم: https://forum.nakra.host
- قناة التليجرام: @nakrahost
- صفحة الفيسبوك: /nakrahost

## التحديثات والصيانة

### التحديثات التلقائية
- تحديثات الأمان: تلقائية
- تحديثات المميزات: شهرية
- إصلاح الأخطاء: أسبوعية

### الصيانة الدورية
- تنظيف قاعدة البيانات
- تحسين الأداء
- مراجعة السجلات
- تحديث النسخ الاحتياطية

## الترخيص

هذا النظام مطور خصيصاً لشركة نقرة للتسويق الإلكتروني.
جميع الحقوق محفوظة © 2024 نقرة للتسويق الإلكتروني

## معلومات الاتصال

**شركة نقرة للتسويق الإلكتروني**
- الموقع الرسمي: https://www.nakraformarketing.com
- البريد الإلكتروني: <EMAIL>
- الهاتف: 01062751630

**شريك الخدمات - هوست ميدو**
- الموقع: https://hostmeed.cloud
- خدمات الاستضافة والسيرفرات

---

تم تطوير هذا النظام بعناية فائقة لضمان أفضل تجربة للمستخدمين العرب في مجال خدمات الاستضافة.
